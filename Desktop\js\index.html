<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        #open{
                         color: white;
         background-color: rgb(13, 13, 61);
        }
        #close{
           color: white;
            position: absolute;
            background-color: rgb(13, 13, 61);
            left: 225px;
        }
        .container {
            width: 100px;
             color: white;
             background-color: rgb(13, 13, 61);
          

        }
        .container p:hover{
            background-color: rgb(75, 75, 168);
        }
        .hide{
            display: none;
        }
    </style>
</head>
<body>
    
     <button class="hide" id="open">open</button>
     <button id="close">close</button>
       <div class="container">
         <p>home</p>
         <p>about</p>
         <p>contact</p>
         <p>projects</p>
       </div>

    <script src="index.js"></script>
</body>
</html>